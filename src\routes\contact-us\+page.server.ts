import type { Actions, PageServerLoad } from './$types';
import { fail } from '@sveltejs/kit';
import { z } from 'zod';
import { superValidate } from 'sveltekit-superforms/server';
import { zod } from 'sveltekit-superforms/adapters';
import { message } from 'sveltekit-superforms';
import nodemailer from 'nodemailer';
import dotenv from 'dotenv';
import { COMPANY_NAME, EMAIL, PHONE_NUMBER, DEBUG_MODE } from '$lib/constants.js';

// Load environment variables
dotenv.config();

// Debug logging function
const debug = (...args: unknown[]) => {
	if (DEBUG_MODE) {
		console.log(...args);
	}
};

// Spam keywords to check for
const SPAM_KEYWORDS = [
	'viagra', 'casino', 'porn', 'sex', 'bitcoin', 'crypto', 'forex', 
	'diet pills', 'weight loss', 'enlargement', 'dating', 'hookup', 'escort',
	'lottery', 'inheritance', 'Google’s 1st Page', 'seo proposal', 'ranking well', 'yahoo, AOL, Bing'
];

// Function to check for spam keywords
const checkForSpamKeywords = (text: string): string[] => {
	const foundKeywords: string[] = [];
	const lowerText = text.toLowerCase();
	
	for (const keyword of SPAM_KEYWORDS) {
		if (lowerText.includes(keyword.toLowerCase())) {
			foundKeywords.push(keyword);
		}
	}
	
	return foundKeywords;
};

const enquirySchema = z.object({
	name: z.string().min(2, 'Name must be at least 2 characters'),
	email: z.string().email('Must be a valid email address format'),
	phone: z.string().min(6, 'Phone number is too short').max(25, 'Phone number is too long'),
	message: z
		.string()
		.min(10, 'Message must be at least 10 characters')
		.max(1500, 'Message is too long, please keep it under 1500 characters')
});

export const load: PageServerLoad = async () => {
	const form = await superValidate(zod(enquirySchema));
	return { form };
};

export const actions: Actions = {
	default: async ({ request }) => {
		debug('📝 Form submission received');

		// Step 1: Parse form data manually for honeypot check
		const rawFormData = await request.formData();
		const honeypot = (rawFormData.get('homepage') || '').toString().trim();

		if (honeypot) {
			debug('🚫 Honeypot triggered – bot submission blocked');
			const form = await superValidate(rawFormData, zod(enquirySchema));
			return fail(400, { 
				form, 
				_errors: ['Spam detected. Submission blocked.'] 
			});
		}

		// Step 2: Validate actual form content
		const form = await superValidate(rawFormData, zod(enquirySchema));

		if (!form.valid) {
			debug('❌ Form validation failed:', form.errors);
			return fail(400, { form });
		}

		// Step 3: Check for spam keywords
		const messageText = form.data.message;
		const nameText = form.data.name;
		const emailText = form.data.email;
		
		// Check all text fields for spam keywords
		const allText = `${nameText} ${emailText} ${messageText}`;
		const foundSpamKeywords = checkForSpamKeywords(allText);
		
		if (foundSpamKeywords.length > 0) {
			debug('🚫 Spam keywords detected:', foundSpamKeywords);
			const keywordList = foundSpamKeywords.join(', ');
			return fail(400, { 
				form, 
				_errors: [`Spam detected. Your message contains potentially inappropriate content: "${keywordList}". Please rephrase your message and try again.`] 
			});
		}

		debug('✅ Form validation passed');
		debug('✅ Spam check passed');
		debug('📧 Form data:', {
			name: form.data.name,
			email: form.data.email,
			phone: form.data.phone,
			messageLength: form.data.message.length
		});

		// Step 4: Check env vars
		const gmailUser = process.env.ENQUIRY_FORM_GMAIL_USER;
		const gmailPassword = process.env.ENQUIRY_FORM_GMAIL_APP_PASSWORD;

		debug('🔍 Environment variables check:');
		debug('ENQUIRY_FORM_GMAIL_USER:', gmailUser ? 'Set' : 'Missing');
		debug(
			'ENQUIRY_FORM_GMAIL_APP_PASSWORD:',
			gmailPassword ? 'Set (length: ' + gmailPassword.length + ')' : 'Missing'
		);

		if (!gmailUser || !gmailPassword) {
			console.error('❌ Missing Gmail environment variables');
			return fail(500, { 
				form, 
				_errors: [`Email service is not configured. Please contact us directly on ${PHONE_NUMBER}.`] 
			});
		}

		debug('✅ Environment variables found');

		const transporter = nodemailer.createTransport({
			service: 'gmail',
			auth: {
				user: gmailUser,
				pass: gmailPassword
			},
			port: 587,
			secure: false,
			requireTLS: true,
			tls: {
				ciphers: 'SSLv3'
			}
		});

		try {
			debug('🔍 Verifying email transporter...');
			await transporter.verify();
			debug('✅ Email transporter verified successfully');
		} catch (error) {
			console.error('❌ Email transporter verification failed:', error);
			return fail(500, { 
				form, 
				_errors: [`Email service configuration error. Please contact us directly on ${PHONE_NUMBER} or try again later.`] 
			});
		}

		const mailOptions = {
			from: `"${COMPANY_NAME}" <${EMAIL}>`,
			replyTo: form.data.email,
			to: gmailUser,
			subject: `Website enquiry from ${form.data.name}`,
			text: `
New website enquiry received:

Name: ${form.data.name}
Email: ${form.data.email}
Phone: ${form.data.phone}

Message:
${form.data.message}

---
This enquiry was submitted through the jdes.co.uk website online enquiry form.
			`,
			html: `
<h2>New Website Enquiry</h2>
<p><strong>Name:</strong> ${form.data.name}</p>
<p><strong>Email:</strong> <a href="mailto:${form.data.email}">${form.data.email}</a></p>
<p><strong>Phone:</strong> <a href="tel:${form.data.phone}">${form.data.phone}</a></p>
<p><strong>Message:</strong></p>
<p>${form.data.message.replace(/\n/g, '<br>')}</p>
<hr>
<p><em>This enquiry was submitted through the jdes.co.uk online enquiry form.</em></p>
			`
		};

		try {
			debug('📧 Sending email...');
			const info = await transporter.sendMail(mailOptions);
			debug('✅ Email sent successfully:', info.messageId);

			const confirmationMailOptions = {
				from: `"${COMPANY_NAME}" <${EMAIL}>`,
				to: form.data.email,
				subject: `Confirmation of your enquiry - ${COMPANY_NAME}`,
				text: `
Dear ${form.data.name},

Thank you for contacting ${COMPANY_NAME} - this is a confirmation that we have received your enquiry and will get back to you as soon as possible.

Your enquiry details:
Name: ${form.data.name}
Phone: ${form.data.phone}
Message: ${form.data.message}

We typically respond within 24-48 hours during business days.
				`,
				html: `
<h2>Thank you for your enquiry</h2>
<p>Dear ${form.data.name},</p>
<p>Thank you for contacting <strong>${COMPANY_NAME}</strong> - this is a confirmation that we have received your enquiry and will get back to you as soon as possible.</p>

<h3>Your enquiry details:</h3>
<ul>
<li><strong>Name:</strong> ${form.data.name}</li>
<li><strong>Phone:</strong> <a href="tel:${form.data.phone}">${form.data.phone}</a></li>
<li><strong>Message:</strong> ${form.data.message.replace(/\n/g, '<br>')}</li>
</ul>

<p>We typically respond within 24-48 hours during business days.</p>
				`
			};

			try {
				debug('📧 Sending confirmation email to customer...');
				const confirmationInfo = await transporter.sendMail(confirmationMailOptions);
				debug('✅ Confirmation email sent successfully:', confirmationInfo.messageId);
			} catch (confirmationError) {
				console.error('⚠️ Error sending confirmation email:', confirmationError);
			}

			return message(
				form,
				'Your enquiry has been sent successfully, and we will get back to you as soon as possible.'
			);
		} catch (error) {
			console.error('❌ Error sending email:', error);
			return fail(500, { 
				form, 
				_errors: [`Sorry, there was an error sending your enquiry. Please try again later or contact us directly on ${PHONE_NUMBER}.`] 
			});
		}
	}
};
