<script>
	const currentYear = new Date().getFullYear();
	import {
		COMPANY_NAME,
		COMPANY_LOCATION,
		COMPANY_DESCRIPTION,
		PHONE_NUMBER,
		EMAIL,
		FACEBOOK_URL,
		GOOGLE_BUSINESS_URL
	} from '$lib/constants.js';
</script>

<footer class="py-10" style="background-color: #c5d6e0;">
	<div class="container mx-auto grid gap-8 px-4 md:grid-cols-3">
		<!-- Company logo -->
		<div class="pl-2 lg:pl-4 xl:pl-8">
			<div class="flex justify-start lg:justify-center">
				<a href="/" aria-label="Go to homepage">
					<img
						src="/footer-logo-branding.png"
						alt="{COMPANY_NAME} logo"
						class="h-48 object-contain"
						loading="lazy"
					/>
				</a>
			</div>
		</div>
		<!-- Description and then company contact details -->
		<div class="space-y-4 pl-8">
			<h3 class="max-w-xs text-sm font-bold">{COMPANY_DESCRIPTION}</h3>

			<ul class="space-y-3">
				<li class="flex items-center gap-3">
					<svg
						xmlns="http://www.w3.org/2000/svg"
						fill="none"
						viewBox="0 0 24 24"
						stroke-width="1.5"
						stroke="currentColor"
						class="h-5 w-5 text-info-content"
						aria-hidden="true"
					>
						<path
							stroke-linecap="round"
							stroke-linejoin="round"
							d="M14.25 9.75v-4.5m0 4.5h4.5m-4.5 0 6-6m-3 18c-8.284 0-15-6.716-15-15V4.5A2.25 2.25 0 0 1 4.5 2.25h1.372c.516 0 .966.351 1.091.852l1.106 4.423c.11.44-.054.902-.417 1.173l-1.293.97a1.062 1.062 0 0 0-.38 1.21 12.035 12.035 0 0 0 7.143 7.143c.441.162.928-.004 1.21-.38l.97-1.293a1.125 1.125 0 0 1 1.173-.417l4.423 1.106c.5.125.852.575.852 1.091V19.5a2.25 2.25 0 0 1-2.25 2.25h-2.25Z"
						/>
					</svg>
					<a href="tel:{PHONE_NUMBER}" class="link-hover link text-sm font-medium">{PHONE_NUMBER}</a
					>
				</li>
				<li class="flex items-center gap-3">
					<svg
						xmlns="http://www.w3.org/2000/svg"
						fill="none"
						viewBox="0 0 24 24"
						stroke-width="1.5"
						stroke="currentColor"
						class="h-5 w-5 text-info-content"
						aria-hidden="true"
					>
						<path
							stroke-linecap="round"
							stroke-linejoin="round"
							d="M21.75 9v.906a2.25 2.25 0 0 1-1.183 1.981l-6.478 3.488M2.25 9v.906a2.25 2.25 0 0 0 1.183 1.981l6.478 3.488m8.839 2.51-4.66-2.51m0 0-1.023-.55a2.25 2.25 0 0 0-2.134 0l-1.022.55m0 0-4.661 2.51m16.5 1.615a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V8.844a2.25 2.25 0 0 1 1.183-1.981l7.5-4.039a2.25 2.25 0 0 1 2.134 0l7.5 4.039a2.25 2.25 0 0 1 1.183 1.98V19.5Z"
						/>
					</svg>
					<a
						href="mailto:{EMAIL}?subject=Website Enquiry"
						class="link-hover link text-sm font-medium">{EMAIL}</a
					>
				</li>
				<li class="flex items-center gap-3">
					<svg
						xmlns="http://www.w3.org/2000/svg"
						fill="none"
						viewBox="0 0 24 24"
						stroke-width="1.5"
						stroke="currentColor"
						class="h-5 w-5 text-info-content"
						aria-hidden="true"
					>
						<path
							stroke-linecap="round"
							stroke-linejoin="round"
							d="M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"
						/>
						<path
							stroke-linecap="round"
							stroke-linejoin="round"
							d="M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z"
						/>
					</svg>
					<span class="text-sm font-medium">{COMPANY_LOCATION}</span>
				</li>
			</ul>
		</div>

		<!-- Links to more information -->
		<div class="space-y-4 pl-8">
			<div class="flex flex-col space-y-1">
				<h3 class="font-bold">More Information</h3>
				<a href="/about-us" class="link-hover link text-sm">About Us</a>
				<a href="/showcase" class="link-hover link text-sm">Showcase</a>
				<a href="/why-choose-us" class="link-hover link text-sm">Why Choose Us</a>
				<a href="/areas-we-cover" class="link-hover link text-sm">Areas We Cover</a>
				<a href="/contact-us" class="link-hover link text-sm">Contact Us</a>
				<a href="/privacy-policy" class="link-hover link text-sm">Privacy Policy</a>
			</div>
			<div class="flex flex-col space-y-1">
				<h3 class="font-bold">Our Services</h3>
				<a href="/domestic" class="link-hover link text-sm">Domestic Electrical</a>
				<a href="/agricultural" class="link-hover link text-sm">Agricultural Electrical</a>
				<a href="/commercial-industrial" class="link-hover link text-sm"
					>Commercial &amp; Industrial Electrical</a
				>
				<a href="/sustainability-resiliency-efficiency" class="link-hover link text-sm">
					Sustainability, Resiliency &amp; Efficiency Services
				</a>
			</div>
		</div>
	</div>

	<!-- Bottom -->
	<div class="mt-8 border-t border-base-200 px-6 pt-6 text-center">
		<p class="text-sm">&copy; {currentYear} {COMPANY_NAME} All rights reserved.</p>
	</div>
</footer>
