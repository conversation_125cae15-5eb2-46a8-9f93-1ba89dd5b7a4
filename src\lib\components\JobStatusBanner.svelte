<script lang="ts">
	import { JOB_STATUS_CONFIG, JOB_STATUS_TITLE, CURRENT_JOB_STATUS } from '$lib/constants.js';

	// Get the current status configuration
	$: statusConfig = JOB_STATUS_CONFIG[CURRENT_JOB_STATUS as keyof typeof JOB_STATUS_CONFIG];
	$: shouldShowBanner = (CURRENT_JOB_STATUS as string) !== 'hide';

	// Icon SVG paths for different status types
	const icons: Record<string, string> = {
		rocket: `M15.59 14.37a6 6 0 0 1-5.84 7.38v-4.8m5.84-2.58a14.98 14.98 0 0 0 6.16-12.12A14.98 14.98 0 0 0 9.631 8.41m5.96 5.96a14.926 14.926 0 0 1-5.841 2.58m-.119-8.54a6 6 0 0 0-7.381 5.84h4.8m2.581-5.84a14.927 14.927 0 0 0-2.58 5.84m2.699 2.7c-.103.021-.207.041-.311.06a15.09 15.09 0 0 1-2.448-2.448 14.9 14.9 0 0 1 .06-.312m-2.24 2.39a4.493 4.493 0 0 0-1.757 4.306 4.493 4.493 0 0 0 4.306-1.758M16.5 9a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0Z`,
		clock: `M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z`,
		'magnifying-glass': `m15.75 15.75-2.489-2.489m0 0a3.375 3.375 0 1 0-4.773-4.773 3.375 3.375 0 0 0 4.774 4.774ZM21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z`
	};
</script>

{#if shouldShowBanner}
	<div class="{statusConfig.bgColor} px-4 py-2 {statusConfig.textColor}">
		<div class="flex items-center justify-center gap-2">
			<svg
				xmlns="http://www.w3.org/2000/svg"
				fill="none"
				viewBox="0 0 24 24"
				stroke-width="1.5"
				stroke="currentColor"
				class="size-5"
			>
				<path stroke-linecap="round" stroke-linejoin="round" d={icons[statusConfig.icon]} />
			</svg>
			<div class="text-sm font-medium">
				<span class="font-semibold">{JOB_STATUS_TITLE}:</span>
				{statusConfig.message}
			</div>
		</div>
	</div>
{/if}
